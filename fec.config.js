const path = require('path')
const refName = process.env.CI_COMMIT_REF_NAME
const isProd = refName === 'master'
const devConfig = require('./build/development.config')
const macDevConfig = require('./build/mac.dev.config')
const proConfig = require('./build/production.config')
const testConfig = require('./build/staging.config')

function resolve(dir) {
  return path.join(__dirname, dir)
}

//  fec 运行变量
// __DEV__  fec d
// __PROD__ fec o
// console.log(process.argv)

const isMac = process.platform === 'darwin'
const host = isMac ? '0.0.0.0' : '127.0.0.1'

// 分支环境变量
const vars = {
  __DEV: typeof refName === 'undefined',
  __TEST: !isProd,
  __PROD: isProd,
  ...(typeof refName === 'undefined' ? (isMac ? macDevConfig : devConfig) : (!isProd ? testConfig : proConfig))
  // ...(typeof refName === 'undefined' ? (true ? macDevConfig : devConfig) : (!isProd ? testConfig : proConfig)) // 前端跑本地项目需要
}
vars.__ENV = isProd ? 'prod' : typeof refName !== 'undefined' ? 'test' : 'dev'

let devServer = {
  host: host
}
if (vars.__ENV === 'dev') {
  devServer.https = false
  const proxyLocal = false
  // 将后台ajax请求代理到哪里
  const target = proxyLocal ? `http://${host}:8080` : 'https://test-manager-hdzt.yy.com'
  devServer.proxy = {
    // detail: https://cli.vuejs.org/config/#devserver-proxy
    // 代理后端请求，将 后端接口 //local.yy.com/api 请求 转发到 http://localhost:8080, 并把 api 去掉（注意，代理到本地服务的时候，不要带 https，本地不支持https）
    [vars.VUE_APP_BASE_API]: {
      target: target,
      changeOrigin: true,
      secure: false, // 这个配置项很关键，它允许将 HTTPS 请求代理到 HTTP 目标地址
      pathRewrite: { // 去掉 /api 前缀，使其能够和管理后台接口服务路径匹配
        ['^' + vars.VUE_APP_BASE_API]: ''
      }
    }
  }
}

// js变量需要 JSON.stringify
function stringifyVar(obj) {
  const result = {}
  var vKeys = Object.keys(obj)
  for (let vKey of vKeys) {
    const item = obj[vKey]
    const type = typeof item
    if (type === 'object' || type === 'string') {
      result[vKey] = JSON.stringify(item)
    } else {
      result[vKey] = item
    }
  }
  return result
}

module.exports = {
  jsx: 'vue',
  outputDir: path.resolve(__dirname, `./output`),
  px2rem: false,
  eslint: true,
  autopolyfill: true,
  devServer: devServer,
  extendWebpack(config) {
    config.output.publicPath(vars.__WEB)
    // set svg-sprite-loader
    config.module
      .rule('svg')
      .exclude.add(resolve('src/assets/icons'))
      .end()
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/assets/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
      .end()

    config.set('externals', {
      isProd: JSON.stringify(isProd),
      jquery: 'jQuery',
      // vue: 'Vue',
      // 'vue-router': 'VueRouter',
      // "element-ui": "ELEMENT"
    })

    // config.module
    //   .rule('sass-resources-loader') // 全局 scss 变量
    //   .test(/\.scss$/)
    //   .use('sass-resources-loader')
    //   .loader('sass-resources-loader')
    //   .options({
    //     resources: './src/css/_util.scss',
    //   })
    config.optimization.runtimeChunk('single'),
      {
         from: path.resolve(__dirname, './public/robots.txt'), //防爬虫文件
         to: './' //到根目录下
      }
  },
  envs: vars, // html变量
  define: stringifyVar(vars), // js变量
  https: !vars.__DEV,
  // https: true // 前端跑本地项目需要
  // plugins: [
  // ],
  // transformModules: [''] //babel node_module
}
